#!/usr/bin/env python3
"""
YouTube Channel Info Extractor
L<PERSON>y thông tin kênh và danh sách URL video từ các kênh YouTube.
"""

import os
import re
import logging
import shutil
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List
from datetime import datetime
import sys
import locale
import requests
from xml.etree import ElementTree

# Đặt encoding mặc định là utf-8
if sys.platform.startswith('win'):
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

import yt_dlp

# Constants
DEFAULT_CHANNEL = "https://www.youtube.com/@SurvivalBushcraftVN"

@dataclass(frozen=True)
class Config:
    """Configuration settings"""
    DOWNLOAD_AVATAR: bool = True

class Logger:
    """Custom logger with color support"""
    def __init__(self, log_file: Path):
        self.logger = logging.getLogger('YouTubeChannelExtractor')
        self.logger.setLevel(logging.INFO)
        
        # File handler với encoding utf-8
        fh = logging.FileHandler(log_file, encoding='utf-8')
        fh.setLevel(logging.INFO)
        fh_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh.setFormatter(fh_formatter)
        self.logger.addHandler(fh)
        
        # Console handler với encoding utf-8
        ch = logging.StreamHandler(sys.stdout)
        ch.setLevel(logging.INFO)
        ch_formatter = logging.Formatter('%(message)s')
        ch.setFormatter(ch_formatter)
        self.logger.addHandler(ch)

    def info(self, msg: str):
        self.logger.info(msg)

    def error(self, msg: str):
        self.logger.error(f"ERROR: {msg}")

class ChannelProcessor:
    """Handles YouTube channel processing"""
    def __init__(self, config: Config, logger: Logger):
        self.config = config
        self.logger = logger
        self.base_path = Path(__file__).parent

    def _sanitize_filename(self, filename: str) -> str:
        """Làm sạch tên file/thư mục để tránh ký tự không hợp lệ"""
        # Thay thế các ký tự không hợp lệ bằng dấu gạch ngang
        invalid_chars = r'[<>:"/\\|?*]'
        return re.sub(invalid_chars, '-', filename).strip()

    def _download_avatar(self, avatar_url: str, save_path: Path) -> bool:
        """Tải avatar của kênh"""
        try:
            response = requests.get(avatar_url)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)  # Sử dụng content để tải nhanh hơn
            return True
        except Exception as e:
            self.logger.error(f"Lỗi khi tải avatar từ {avatar_url}: {str(e)}")
            return False

    def _get_channel_info(self, channel_url: str) -> Dict:
        """Get channel information and video list using uploads playlist"""
        ydl_opts = {
            'quiet': True,
            'extract_flat': 'in_playlist',
            'force_generic_extractor': False,
            'ignoreerrors': True,
            'no_warnings': True,
            'playlistreverse': False,
        }

        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                self.logger.info(f"[1/4] Đang lấy thông tin kênh...")
                
                # Lấy thông tin kênh và danh sách video cùng một lúc
                channel_info = ydl.extract_info(channel_url, download=False)
                channel_id = channel_info.get('channel_id', '')
                if not channel_id and 'entries' in channel_info:
                    first_video = channel_info['entries'][0]
                    channel_id = first_video.get('channel_id', '')

                if not channel_id:
                    self.logger.error("❌ Lỗi: Không tìm thấy channel ID")
                    return None

                # Lấy danh sách video từ playlist Uploads
                self.logger.info(f"[2/4] Đang lấy danh sách video...")
                uploads_playlist = f"UU{channel_id[2:]}"
                playlist_url = f"https://www.youtube.com/playlist?list={uploads_playlist}"
                uploads_info = ydl.extract_info(playlist_url, download=False)
                
                # Lọc video theo độ dài và loại
                uploads_info['entries'] = [
                    entry for entry in uploads_info.get('entries', [])
                    if entry.get('duration') is not None and  # Kiểm tra duration không phải là None
                    60 <= entry.get('duration', 0) <= 3600 and not entry.get('live', False)
                ]
                
                # Lấy thông tin chi tiết của kênh từ /about
                try:
                    about_url = f"https://www.youtube.com/channel/{channel_id}/about"
                    about_info = ydl.extract_info(about_url, download=False)
                    
                    channel_details = {
                        'channel': about_info.get('channel', channel_info.get('channel', '')),
                        'channel_id': channel_id,
                        'channel_url': about_info.get('channel_url', channel_info.get('channel_url', '')),
                        'description': about_info.get('description', ''),
                        'thumbnails': about_info.get('thumbnails', channel_info.get('thumbnails', []))
                    }
                except Exception as e:
                    self.logger.error(f"⚠️ Không lấy được mô tả kênh từ {about_url}: {str(e)}")
                    channel_details = {
                        'channel': channel_info.get('channel', ''),
                        'channel_id': channel_id,
                        'channel_url': channel_info.get('channel_url', ''),
                        'description': channel_info.get('description', ''),
                        'thumbnails': channel_info.get('thumbnails', [])
                    }
                
                # Kết hợp thông tin
                uploads_info.update(channel_details)
                
                total_videos = len(uploads_info.get('entries', []))
                self.logger.info(f"✓ Tìm thấy {total_videos} video")
                return uploads_info

        except Exception as e:
            self.logger.error(f"❌ Lỗi khi lấy thông tin kênh từ {channel_url}: {str(e)}")
            return None

    def _save_channel_info(self, channel_info: Dict):
        """Save channel information and video URLs"""
        if not channel_info:
            return

        channel_name = channel_info.get('channel', 'unknown_channel')
        safe_channel_name = self._sanitize_filename(channel_name)
        channel_dir = self.base_path / safe_channel_name
        channel_dir.mkdir(parents=True, exist_ok=True)
        
        new_folder = channel_dir / 'New Folder'
        new_folder.mkdir(exist_ok=True)

        try:
            self.logger.info(f"[3/4] Đang lưu thông tin kênh...")
            with open(new_folder / "description.txt", 'w', encoding='utf-8') as f:
                f.write(f"Tên kênh: {channel_info.get('channel', 'N/A')}\n")
                f.write(f"URL kênh: {channel_info.get('channel_url', 'N/A')}\n")
                f.write(f"ID kênh: {channel_info.get('channel_id', 'N/A')}\n\n")
                f.write("Mô tả kênh:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{channel_info.get('description', 'N/A')}\n")
            
            with open(new_folder / "videos.txt", 'w', encoding='utf-8') as f:
                entries = channel_info.get('entries', [])
                for entry in entries:
                    video_id = entry['id']
                    video_url = f"https://www.youtube.com/watch?v={video_id}"
                    f.write(f"{video_url}\n")
            
            if self.config.DOWNLOAD_AVATAR:
                self.logger.info(f"[4/4] Đang tải avatar...")
                avatar_url = channel_info.get('thumbnails', [{}])[-1].get('url')
                if avatar_url:
                    avatar_path = new_folder / "avatar.jpg"
                    if self._download_avatar(avatar_url, avatar_path):
                        self.logger.info("✓ Đã tải avatar")
            
            self.logger.info(f"✓ Hoàn thành! Đã lưu {len(entries)} video vào {new_folder}")
            
        except Exception as e:
            self.logger.error(f"❌ Lỗi khi lưu dữ liệu vào {new_folder}: {str(e)}")

    def process_channel(self, channel_url: str):
        """Process a single channel"""
        self.logger.info(f"\n{'='*50}")
        self.logger.info(f"Xử lý kênh: {channel_url}")
        self.logger.info(f"{'='*50}")
        
        channel_info = self._get_channel_info(channel_url)
        if channel_info:
            self._save_channel_info(channel_info)

def main():
    # Setup
    base_path = Path(__file__).parent
    log_file = base_path / 'channel_extractor.log'
    logger = Logger(log_file)
    config = Config()
    
    # Create processor
    processor = ChannelProcessor(config, logger)
    
    # Read channels from file
    channels_file = base_path / 'channels.txt'
    if not channels_file.exists():
        logger.info(f"File {channels_file} không tồn tại. Sử dụng kênh mặc định.")
        channels = [DEFAULT_CHANNEL]
    else:
        with open(channels_file, 'r', encoding='utf-8') as f:
            channels = [line.strip() for line in f if line.strip()]
    
    # Process each channel
    for channel in channels:
        processor.process_channel(channel)

if __name__ == "__main__":
    main()
